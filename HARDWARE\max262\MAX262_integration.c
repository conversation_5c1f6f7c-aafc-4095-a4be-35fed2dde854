//-----------------------------------------------------------------
// MAX262集成示例 - 与主程序的滤波器学习功能集成
// 文件名: MAX262_integration.c
// 说明: 展示如何将MAX262与现有的滤波器识别功能集成
//-----------------------------------------------------------------

#include "MAX262_STM32F407.h"
#include "stdio.h"

// 外部变量声明 (来自main.c)
extern float max_voltage_ratio;
extern float center_frequency;
extern float quality_factor;

//-----------------------------------------------------------------
// 全局变量
//-----------------------------------------------------------------
static uint8_t max262_initialized = 0;
static float last_configured_freq = 0.0f;
static float last_configured_q = 0.0f;
static uint8_t last_configured_mode = 0xFF;

//-----------------------------------------------------------------
// MAX262初始化和配置函数
//-----------------------------------------------------------------

/**
 * @brief  初始化MAX262硬件
 * @param  None
 * @retval 0: 成功, 1: 失败
 */
uint8_t MAX262_System_Init(void)
{
    if (max262_initialized)
        return 0;  // 已经初始化
    
    printf("=== MAX262系统初始化 ===\r\n");
    
    // 初始化MAX262硬件
    MAX262_Init();
    
    // 标记为已初始化
    max262_initialized = 1;
    
    printf("MAX262硬件初始化完成\r\n");
    printf("使用引脚配置:\r\n");
    printf("  数据线: PB0(D0), PB1(D1)\r\n");
    printf("  地址线: PB2(A0), PB3(A1), PB4(A2), PB5(A3)\r\n");
    printf("  控制线: PC0(LE), PC2(WR)\r\n");
    printf("MAX262系统初始化完成\r\n\r\n");
    
    return 0;
}

/**
 * @brief  根据学习到的滤波器参数配置MAX262
 * @param  learned_freq: 学习到的中心频率/截止频率 (Hz)
 * @param  learned_q: 学习到的品质因数
 * @param  filter_type: 滤波器类型 (0:低通, 1:高通, 2:带通, 3:带阻)
 * @retval 0: 成功, 1: 参数错误, 2: 未初始化
 */
uint8_t MAX262_Configure_From_Learning(float learned_freq, float learned_q, uint8_t filter_type)
{
    if (!max262_initialized)
    {
        printf("错误: MAX262未初始化，请先调用MAX262_System_Init()\r\n");
        return 2;
    }
    
    // 参数有效性检查
    if (learned_freq <= 0 || learned_freq > 50000.0f)
    {
        printf("错误: 频率参数超出范围 (0-50kHz): %.2f Hz\r\n", learned_freq);
        return 1;
    }
    
    if (learned_q <= 0.1f || learned_q > 64.0f)
    {
        printf("错误: Q值参数超出范围 (0.1-64): %.2f\r\n", learned_q);
        return 1;
    }
    
    if (filter_type > 3)
    {
        printf("错误: 滤波器类型无效: %d\r\n", filter_type);
        return 1;
    }
    
    // 检查是否需要重新配置
    if (learned_freq == last_configured_freq && 
        learned_q == last_configured_q && 
        filter_type == last_configured_mode)
    {
        printf("MAX262配置未改变，跳过重新配置\r\n");
        return 0;
    }
    
    printf("=== 配置MAX262滤波器 ===\r\n");
    
    // 打印配置信息
    const char* filter_names[] = {"低通", "高通", "带通", "带阻"};
    printf("滤波器类型: %s\r\n", filter_names[filter_type]);
    printf("中心频率/截止频率: %.2f Hz\r\n", learned_freq);
    printf("品质因数Q: %.2f\r\n", learned_q);
    
    // 配置滤波器1 (使用模式1)
    printf("配置滤波器1 (模式1)...\r\n");
    MAX262_Filter1_Config(filter_type, learned_freq, learned_q);
    
    // 配置滤波器2 (使用模式3)
    printf("配置滤波器2 (模式3)...\r\n");
    MAX262_Filter2_Config(filter_type, learned_freq, learned_q);
    
    // 保存配置参数
    last_configured_freq = learned_freq;
    last_configured_q = learned_q;
    last_configured_mode = filter_type;
    
    printf("MAX262配置完成\r\n\r\n");
    
    return 0;
}

/**
 * @brief  自动从主程序的滤波器识别结果配置MAX262
 * @param  None
 * @retval 0: 成功, 1: 无有效数据, 2: 配置失败
 */
uint8_t MAX262_Auto_Configure_From_Main(void)
{
    // 检查是否有有效的识别结果
    if (max_voltage_ratio <= 0.0f)
    {
        printf("警告: 没有有效的滤波器识别数据\r\n");
        return 1;
    }
    
    // 使用全局变量中的识别结果
    float freq = center_frequency;
    float q = quality_factor;
    
    // 根据频率响应特征判断滤波器类型
    uint8_t filter_type = 2;  // 默认为带通滤波器
    
    // 简单的滤波器类型判断逻辑 (可以根据实际需要改进)
    if (q < 0.8f)
    {
        filter_type = 0;  // 低Q值，可能是低通滤波器
    }
    else if (q > 5.0f)
    {
        filter_type = 2;  // 高Q值，可能是带通滤波器
    }
    else
    {
        filter_type = 1;  // 中等Q值，可能是高通滤波器
    }
    
    printf("=== 自动配置MAX262 ===\r\n");
    printf("从主程序识别结果自动配置:\r\n");
    printf("  识别频率: %.2f Hz\r\n", freq);
    printf("  识别Q值: %.2f\r\n", q);
    printf("  推断类型: %d\r\n", filter_type);
    
    // 调用配置函数
    return MAX262_Configure_From_Learning(freq, q, filter_type);
}

/**
 * @brief  测试MAX262配置
 * @param  None
 * @retval None
 */
void MAX262_Test_Configuration(void)
{
    printf("=== MAX262配置测试 ===\r\n");
    
    // 测试不同类型的滤波器配置
    float test_frequencies[] = {1000.0f, 2000.0f, 5000.0f, 10000.0f};
    float test_q_values[] = {0.707f, 1.0f, 2.0f, 5.0f};
    uint8_t test_types[] = {0, 1, 2, 3};  // 低通, 高通, 带通, 带阻
    
    for (int i = 0; i < 4; i++)
    {
        printf("\n--- 测试配置 %d ---\r\n", i + 1);
        MAX262_Configure_From_Learning(test_frequencies[i], test_q_values[i], test_types[i]);
        delay_ms(1000);  // 等待1秒
    }
    
    printf("MAX262配置测试完成\r\n\r\n");
}

/**
 * @brief  获取MAX262状态信息
 * @param  None
 * @retval None
 */
void MAX262_Print_Status(void)
{
    printf("=== MAX262状态信息 ===\r\n");
    printf("初始化状态: %s\r\n", max262_initialized ? "已初始化" : "未初始化");
    
    if (max262_initialized)
    {
        printf("最后配置频率: %.2f Hz\r\n", last_configured_freq);
        printf("最后配置Q值: %.2f\r\n", last_configured_q);
        printf("最后配置模式: %d\r\n", last_configured_mode);
        
        const char* mode_names[] = {"低通", "高通", "带通", "带阻"};
        if (last_configured_mode <= 3)
        {
            printf("滤波器类型: %s\r\n", mode_names[last_configured_mode]);
        }
    }
    
    printf("时钟频率配置:\r\n");
    printf("  滤波器1时钟: %.1f kHz\r\n", MAX262_CLK_FREQ_1 / 1000.0f);
    printf("  滤波器2时钟: %.1f kHz\r\n", MAX262_CLK_FREQ_2 / 1000.0f);
    printf("========================\r\n\r\n");
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// MAX262驱动程序 - 适配STM32F407ZG
// 文件名: MAX262_STM32F407.c
// 作    者: 正点原子 (修改适配STM32F407ZG)
// 编写时间: 2014-01-28
// 修改记录: 2024-08-03 - 适配STM32F407ZG，增加频率计算功能
//-----------------------------------------------------------------

#include "MAX262_STM32F407.h"

//-----------------------------------------------------------------
// GPIO初始化函数
//-----------------------------------------------------------------
void MAX262_GPIO_Init(void)
{
    // 使能GPIO时钟
    MAX262_D0_GPIO_CLK_ENABLE();
    MAX262_D1_GPIO_CLK_ENABLE();
    MAX262_A0_GPIO_CLK_ENABLE();
    MAX262_A1_GPIO_CLK_ENABLE();
    MAX262_A2_GPIO_CLK_ENABLE();
    MAX262_A3_GPIO_CLK_ENABLE();
    MAX262_LE_GPIO_CLK_ENABLE();
    MAX262_WR_GPIO_CLK_ENABLE();
    
    // 配置数据引脚为输出
    sys_gpio_set(MAX262_D0_GPIO_PORT, MAX262_D0_GPIO_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_D1_GPIO_PORT, MAX262_D1_GPIO_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    
    // 配置地址引脚为输出
    sys_gpio_set(MAX262_A0_GPIO_PORT, MAX262_A0_GPIO_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_A1_GPIO_PORT, MAX262_A1_GPIO_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_A2_GPIO_PORT, MAX262_A2_GPIO_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_A3_GPIO_PORT, MAX262_A3_GPIO_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    
    // 配置控制引脚为输出
    sys_gpio_set(MAX262_LE_GPIO_PORT, MAX262_LE_GPIO_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    sys_gpio_set(MAX262_WR_GPIO_PORT, MAX262_WR_GPIO_PIN, 
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_NONE);
    
    // 初始化引脚状态
    LE_H;   // 锁存使能高电平
    WR_H;   // 写使能高电平
    
    // 清零所有地址和数据线
    A0_L; A1_L; A2_L; A3_L;
    D0_L; D1_L;
}

//-----------------------------------------------------------------
// MAX262初始化函数
//-----------------------------------------------------------------
void MAX262_Init(void)
{
    MAX262_GPIO_Init();
    delay_ms(10);  // 等待稳定
}

//-----------------------------------------------------------------
// 计算频率控制字Fn
// 参数: frequency - 目标频率 (Hz)
//       clk_freq - 时钟频率 (Hz)
// 返回: 8位频率控制字
//-----------------------------------------------------------------
uint8_t MAX262_Fn_Calculate(float frequency, float clk_freq)
{
    float fn_float;
    uint8_t fn;
    
    // 根据MAX262数据手册: f0 = (Fn/256) * fCLK / 2
    // 所以: Fn = (2 * f0 * 256) / fCLK
    fn_float = (2.0f * frequency * 256.0f) / clk_freq;
    
    // 限制范围在1-255之间
    if (fn_float < 1.0f) fn_float = 1.0f;
    if (fn_float > 255.0f) fn_float = 255.0f;
    
    fn = (uint8_t)(fn_float + 0.5f);  // 四舍五入
    
    return fn;
}

//-----------------------------------------------------------------
// 计算Q值控制字Qn
// 参数: q - 品质因数Q
// 返回: 8位Q控制字
//-----------------------------------------------------------------
uint8_t MAX262_Qn_Calculate(float q)
{
    uint8_t qn;
    
    // 根据MAX262数据手册: Q = 64/(128-Qn)
    // 所以: Qn = 128 - 64/Q
    if (q < 0.5f) q = 0.5f;  // 最小Q值限制
    if (q > 64.0f) q = 64.0f;  // 最大Q值限制
    
    qn = (uint8_t)(128.0f - 64.0f/q + 0.5f);  // 四舍五入
    
    return qn;
}

//-----------------------------------------------------------------
// 写寄存器函数
// 参数: address - 寄存器地址 (0-15)
//       data - 要写入的数据 (8位，但只使用低2位)
//-----------------------------------------------------------------
void MAX262_Write_Register(uint8_t address, uint8_t data)
{
    // 设置地址
    if (address & 0x01) A0_H; else A0_L;
    if (address & 0x02) A1_H; else A1_L;
    if (address & 0x04) A2_H; else A2_L;
    if (address & 0x08) A3_H; else A3_L;
    
    delay_us(1);  // 地址建立时间

    // 写使能低电平
    WR_L;
    delay_us(1);

    // 设置数据 (只使用D0和D1)
    if (data & 0x01) D0_H; else D0_L;
    if (data & 0x02) D1_H; else D1_L;

    delay_us(1);  // 数据建立时间

    // 写使能高电平
    WR_H;
    delay_us(1);
}

//-----------------------------------------------------------------
// 配置滤波器1
// 参数: mode - 滤波器模式 (0-3)
//       frequency - 中心频率/截止频率 (Hz)
//       q - 品质因数
//-----------------------------------------------------------------
void MAX262_Filter1_Config(uint8_t mode, float frequency, float q)
{
    uint8_t fn, qn;
    uint8_t i;
    
    // 计算频率和Q值控制字
    fn = MAX262_Fn_Calculate(frequency, MAX262_CLK_FREQ_1);
    qn = MAX262_Qn_Calculate(q);
    
    // 使能锁存
    LE_H;
    delay_us(1);
    
    // 写模式寄存器 (地址0)
    MAX262_Write_Register(0, mode & 0x03);
    
    // 写频率控制字 (地址1-3, 每个地址写入2位)
    for (i = 0; i < 3; i++)
    {
        uint8_t fn_bits = (fn >> (i * 2)) & 0x03;
        MAX262_Write_Register(i + 1, fn_bits);
    }
    
    // 写Q控制字 (地址4-7, 每个地址写入2位)
    for (i = 0; i < 4; i++)
    {
        uint8_t qn_bits = (qn >> (i * 2)) & 0x03;
        MAX262_Write_Register(i + 4, qn_bits);
    }
}

//-----------------------------------------------------------------
// 配置滤波器2
// 参数: mode - 滤波器模式 (0-3)
//       frequency - 中心频率/截止频率 (Hz)
//       q - 品质因数
//-----------------------------------------------------------------
void MAX262_Filter2_Config(uint8_t mode, float frequency, float q)
{
    uint8_t fn, qn;
    uint8_t i;
    
    // 计算频率和Q值控制字
    fn = MAX262_Fn_Calculate(frequency, MAX262_CLK_FREQ_2);
    qn = MAX262_Qn_Calculate(q);
    
    // 使能锁存
    LE_H;
    delay_us(1);
    
    // 写模式寄存器 (地址8)
    MAX262_Write_Register(8, mode & 0x03);
    
    // 写频率控制字 (地址9-11, 每个地址写入2位)
    for (i = 0; i < 3; i++)
    {
        uint8_t fn_bits = (fn >> (i * 2)) & 0x03;
        MAX262_Write_Register(i + 9, fn_bits);
    }
    
    // 写Q控制字 (地址12-15, 每个地址写入2位)
    for (i = 0; i < 4; i++)
    {
        uint8_t qn_bits = (qn >> (i * 2)) & 0x03;
        MAX262_Write_Register(i + 12, qn_bits);
    }
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------

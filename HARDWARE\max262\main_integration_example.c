//-----------------------------------------------------------------
// 主程序集成示例 - 展示如何在main.c中添加MAX262功能
// 文件名: main_integration_example.c
// 说明: 这个文件展示了需要在main.c中添加的代码
//-----------------------------------------------------------------

/*
在你的main.c文件中添加以下代码：

1. 在文件顶部的#include部分添加：
*/
#include "MAX262_integration.h"
#include "MAX262_clock.h"

/*
2. 在main函数的初始化部分添加MAX262初始化：
*/
int main(void)
{
    // ... 你现有的初始化代码 ...
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    printf("System Starting...\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    Adc2_Init();
    DAC_PA4_Init();
    DAC_SineWave_Init();
    DAC_SetUserEnable(0);
    Adc3_Init();

    // ... 其他现有初始化代码 ...
    AD9833_Init();
    AD9833_Init1();
    key_config();
    lcd_init();

    // *** 添加MAX262初始化 ***
    printf("初始化MAX262滤波器和时钟系统...\r\n");
    if (MAX262_System_Init() == 0)
    {
        printf("MAX262初始化成功\r\n");
        MAX262_Print_Status();  // 打印状态信息
        MAX262_Clock_PrintStatus();  // 打印时钟状态

        // 可选：运行快速时钟测试
        // MAX262_Clock_Quick_Test();
    }
    else
    {
        printf("MAX262初始化失败\r\n");
    }

    // ... 你现有的其他初始化代码 ...
    sampfre = 815534;
    TIM3_Int_Init(103 - 1, 1 - 1);
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);
    TIM6_DAC_Init(10 - 1, 84 - 1);

    // ... 现有的UI初始化代码 ...
    lcd_clear(WHITE);
    g_point_color = BLACK;
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);
    draw_all_buttons(selected_button);
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);
    DAC_SetSineFrequency(current_frequency);

    // ... 进入主循环 ...
    while(1)
    {
        // ... 你现有的主循环代码 ...
        
        // *** 在适当的位置添加MAX262配置代码 ***
        // 例如，在滤波器识别完成后
        
        // ... 现有的代码 ...
    }
}

/*
3. 在IdentifyCircuitModel函数的末尾添加MAX262配置：
*/
void IdentifyCircuitModel(void)
{
    printf("=== CIRCUIT MODEL IDENTIFICATION ===\r\n");

    // 检查是否有足够的数据
    if (max_voltage_ratio <= 0.0f) {
        printf("Error: No valid sweep data available\r\n");
        return;
    }

    // 计算滤波器参数
    CalculateFilterParameters();

    // 显示传递函数
    DisplayTransferFunction();

    // *** 添加MAX262自动配置 ***
    printf("\n=== 配置MAX262滤波器 ===\r\n");
    if (MAX262_Auto_Configure_From_Main() == 0)
    {
        printf("MAX262配置成功，滤波器已准备就绪\r\n");
    }
    else
    {
        printf("MAX262配置失败，请检查参数\r\n");
    }

    printf("=== END OF IDENTIFICATION ===\r\n");
}

/*
4. 可选：添加按键控制MAX262的功能
在你的按键处理代码中添加：
*/
void Handle_MAX262_Keys(void)
{
    // 示例：使用某个按键来测试MAX262配置
    static uint8_t test_mode = 0;
    
    if (/* 某个按键被按下 */)
    {
        printf("测试MAX262配置 %d\r\n", test_mode);
        
        switch(test_mode)
        {
            case 0:
                // 1kHz低通滤波器
                MAX262_Configure_From_Learning(1000.0f, 0.707f, 0);
                break;
            case 1:
                // 2kHz高通滤波器
                MAX262_Configure_From_Learning(2000.0f, 1.0f, 1);
                break;
            case 2:
                // 5kHz带通滤波器
                MAX262_Configure_From_Learning(5000.0f, 2.0f, 2);
                break;
            case 3:
                // 10kHz带阻滤波器
                MAX262_Configure_From_Learning(10000.0f, 1.5f, 3);
                break;
        }
        
        test_mode = (test_mode + 1) % 4;
    }
}

/*
5. 可选：添加串口命令控制MAX262
在你的串口处理代码中添加：
*/
void Handle_MAX262_Commands(char* command)
{
    if (strncmp(command, "MAX262", 6) == 0)
    {
        if (strstr(command, "STATUS"))
        {
            MAX262_Print_Status();
        }
        else if (strstr(command, "TEST"))
        {
            MAX262_Test_Configuration();
        }
        else if (strstr(command, "AUTO"))
        {
            MAX262_Auto_Configure_From_Main();
        }
        else if (strstr(command, "CONFIG"))
        {
            // 解析配置命令，例如: "MAX262 CONFIG 1000 0.707 0"
            float freq, q;
            int type;
            if (sscanf(command, "MAX262 CONFIG %f %f %d", &freq, &q, &type) == 3)
            {
                MAX262_Configure_From_Learning(freq, q, (uint8_t)type);
            }
            else
            {
                printf("用法: MAX262 CONFIG <频率> <Q值> <类型>\r\n");
                printf("类型: 0=低通, 1=高通, 2=带通, 3=带阻\r\n");
            }
        }
        else if (strstr(command, "CLOCK"))
        {
            if (strstr(command, "STATUS"))
            {
                MAX262_Clock_PrintStatus();
            }
            else if (strstr(command, "SET1"))
            {
                // 设置时钟1频率，例如: "MAX262 CLOCK SET1 100000"
                float freq;
                if (sscanf(command, "MAX262 CLOCK SET1 %f", &freq) == 1)
                {
                    MAX262_Clock1_SetFrequency(freq);
                }
                else
                {
                    printf("用法: MAX262 CLOCK SET1 <频率>\r\n");
                }
            }
            else if (strstr(command, "SET2"))
            {
                // 设置时钟2频率，例如: "MAX262 CLOCK SET2 50000"
                float freq;
                if (sscanf(command, "MAX262 CLOCK SET2 %f", &freq) == 1)
                {
                    MAX262_Clock2_SetFrequency(freq);
                }
                else
                {
                    printf("用法: MAX262 CLOCK SET2 <频率>\r\n");
                }
            }
            else if (strstr(command, "START"))
            {
                MAX262_Clock1_Start();
                MAX262_Clock2_Start();
                printf("时钟输出已启动\r\n");
            }
            else if (strstr(command, "STOP"))
            {
                MAX262_Clock1_Stop();
                MAX262_Clock2_Stop();
                printf("时钟输出已停止\r\n");
            }
            else if (strstr(command, "AUTO"))
            {
                // 自动设置时钟，例如: "MAX262 CLOCK AUTO 1000"
                float freq;
                if (sscanf(command, "MAX262 CLOCK AUTO %f", &freq) == 1)
                {
                    MAX262_Clock_AutoSet(freq);
                }
                else
                {
                    printf("用法: MAX262 CLOCK AUTO <滤波器频率>\r\n");
                }
            }
            else
            {
                printf("时钟命令:\r\n");
                printf("  MAX262 CLOCK STATUS - 显示时钟状态\r\n");
                printf("  MAX262 CLOCK SET1 <freq> - 设置时钟1频率\r\n");
                printf("  MAX262 CLOCK SET2 <freq> - 设置时钟2频率\r\n");
                printf("  MAX262 CLOCK START - 启动时钟输出\r\n");
                printf("  MAX262 CLOCK STOP - 停止时钟输出\r\n");
                printf("  MAX262 CLOCK AUTO <freq> - 自动设置时钟\r\n");
            }
        }
        else
        {
            printf("MAX262命令:\r\n");
            printf("  MAX262 STATUS - 显示状态\r\n");
            printf("  MAX262 TEST - 测试配置\r\n");
            printf("  MAX262 AUTO - 自动配置\r\n");
            printf("  MAX262 CONFIG <freq> <q> <type> - 手动配置\r\n");
            printf("  MAX262 CLOCK ... - 时钟控制命令\r\n");
        }
    }
}

/*
6. 编译注意事项：

确保在项目设置中包含了以下文件：
- MAX262_STM32F407.c
- MAX262_integration.c

并且包含路径中有：
- ..\HARDWARE\max262

如果编译时出现错误，检查：
1. 所有头文件是否正确包含
2. 引脚定义是否与你的硬件匹配
3. 时钟频率设置是否正确

7. 硬件连接：

按照README.md中的引脚连接表连接MAX262模块到STM32F407ZG开发板。

8. 测试步骤：

1) 编译并下载程序
2) 连接串口查看初始化信息
3) 运行滤波器识别功能
4) 观察MAX262是否自动配置
5) 可以通过串口命令手动测试不同配置

*/

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------

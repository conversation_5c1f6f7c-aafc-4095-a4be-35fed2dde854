//-----------------------------------------------------------------
// MAX262时钟生成模块 - STM32F407ZG
// 文件名: MAX262_clock.c
// 说明: 使用STM32F407ZG的定时器PWM功能为MAX262生成时钟信号
//-----------------------------------------------------------------

#include "MAX262_clock.h"
#include "delay.h"
#include "usart.h"
#include <stdio.h>

//-----------------------------------------------------------------
// 全局变量
//-----------------------------------------------------------------
static float current_clk1_freq = 0.0f;
static float current_clk2_freq = 0.0f;
static uint8_t clk1_enabled = 0;
static uint8_t clk2_enabled = 0;

//-----------------------------------------------------------------
// 私有函数声明
//-----------------------------------------------------------------
static void MAX262_TIM1_PWM_Init(uint32_t arr, uint32_t psc);
static void MAX262_TIM2_PWM_Init(uint32_t arr, uint32_t psc);
static uint8_t MAX262_Calculate_PWM_Params(float target_freq, uint32_t tim_clk, uint32_t* arr, uint32_t* psc);

//-----------------------------------------------------------------
// 公共函数实现
//-----------------------------------------------------------------

/**
 * @brief  初始化MAX262时钟生成器
 * @param  None
 * @retval 0: 成功, 1: 失败
 */
uint8_t MAX262_Clock_Init(void)
{
    printf("=== MAX262时钟生成器初始化 ===\r\n");
    
    // 设置默认频率
    if (MAX262_Clock1_SetFrequency(MAX262_DEFAULT_CLK1_FREQ) != 0)
    {
        printf("错误: 时钟1初始化失败\r\n");
        return 1;
    }
    
    if (MAX262_Clock2_SetFrequency(MAX262_DEFAULT_CLK2_FREQ) != 0)
    {
        printf("错误: 时钟2初始化失败\r\n");
        return 1;
    }
    
    printf("时钟1频率: %.1f Hz (引脚PA8)\r\n", current_clk1_freq);
    printf("时钟2频率: %.1f Hz (引脚PA0)\r\n", current_clk2_freq);
    printf("MAX262时钟生成器初始化完成\r\n\r\n");
    
    return 0;
}

/**
 * @brief  设置时钟1频率
 * @param  frequency: 目标频率 (Hz)
 * @retval 0: 成功, 1: 频率超出范围
 */
uint8_t MAX262_Clock1_SetFrequency(float frequency)
{
    uint32_t arr, psc;
    
    // 检查频率范围
    if (frequency < MAX262_MIN_CLK_FREQ || frequency > MAX262_MAX_CLK_FREQ)
    {
        printf("错误: 时钟1频率超出范围 (%.1f - %.1f Hz): %.1f Hz\r\n", 
               MAX262_MIN_CLK_FREQ, MAX262_MAX_CLK_FREQ, frequency);
        return 1;
    }
    
    // 计算PWM参数
    if (MAX262_Calculate_PWM_Params(frequency, TIM1_CLK_FREQ, &arr, &psc) != 0)
    {
        printf("错误: 无法为时钟1计算合适的PWM参数\r\n");
        return 1;
    }
    
    // 初始化TIM1 PWM
    MAX262_TIM1_PWM_Init(arr, psc);
    
    // 保存当前频率
    current_clk1_freq = frequency;
    
    printf("时钟1设置为: %.1f Hz (ARR=%lu, PSC=%lu)\r\n", frequency, arr, psc);
    
    return 0;
}

/**
 * @brief  设置时钟2频率
 * @param  frequency: 目标频率 (Hz)
 * @retval 0: 成功, 1: 频率超出范围
 */
uint8_t MAX262_Clock2_SetFrequency(float frequency)
{
    uint32_t arr, psc;
    
    // 检查频率范围
    if (frequency < MAX262_MIN_CLK_FREQ || frequency > MAX262_MAX_CLK_FREQ)
    {
        printf("错误: 时钟2频率超出范围 (%.1f - %.1f Hz): %.1f Hz\r\n", 
               MAX262_MIN_CLK_FREQ, MAX262_MAX_CLK_FREQ, frequency);
        return 1;
    }
    
    // 计算PWM参数
    if (MAX262_Calculate_PWM_Params(frequency, TIM2_CLK_FREQ, &arr, &psc) != 0)
    {
        printf("错误: 无法为时钟2计算合适的PWM参数\r\n");
        return 1;
    }
    
    // 初始化TIM2 PWM
    MAX262_TIM2_PWM_Init(arr, psc);
    
    // 保存当前频率
    current_clk2_freq = frequency;
    
    printf("时钟2设置为: %.1f Hz (ARR=%lu, PSC=%lu)\r\n", frequency, arr, psc);
    
    return 0;
}

/**
 * @brief  启动时钟1输出
 * @param  None
 * @retval None
 */
void MAX262_Clock1_Start(void)
{
    TIM_Cmd(TIM1, ENABLE);
    clk1_enabled = 1;
    printf("时钟1输出启动 (%.1f Hz)\r\n", current_clk1_freq);
}

/**
 * @brief  停止时钟1输出
 * @param  None
 * @retval None
 */
void MAX262_Clock1_Stop(void)
{
    TIM_Cmd(TIM1, DISABLE);
    clk1_enabled = 0;
    printf("时钟1输出停止\r\n");
}

/**
 * @brief  启动时钟2输出
 * @param  None
 * @retval None
 */
void MAX262_Clock2_Start(void)
{
    TIM_Cmd(TIM2, ENABLE);
    clk2_enabled = 1;
    printf("时钟2输出启动 (%.1f Hz)\r\n", current_clk2_freq);
}

/**
 * @brief  停止时钟2输出
 * @param  None
 * @retval None
 */
void MAX262_Clock2_Stop(void)
{
    TIM_Cmd(TIM2, DISABLE);
    clk2_enabled = 0;
    printf("时钟2输出停止\r\n");
}

/**
 * @brief  获取当前时钟1频率
 * @param  None
 * @retval 当前时钟1频率 (Hz)
 */
float MAX262_Clock1_GetFrequency(void)
{
    return current_clk1_freq;
}

/**
 * @brief  获取当前时钟2频率
 * @param  None
 * @retval 当前时钟2频率 (Hz)
 */
float MAX262_Clock2_GetFrequency(void)
{
    return current_clk2_freq;
}

/**
 * @brief  根据滤波器频率自动设置时钟频率
 * @param  filter_freq: 滤波器工作频率 (Hz)
 * @retval 0: 成功, 1: 频率超出范围
 * @note   时钟频率 = 滤波器频率 × 倍数因子
 */
uint8_t MAX262_Clock_AutoSet(float filter_freq)
{
    // MAX262推荐的时钟频率倍数
    float clk1_multiplier = 139.8f;  // 对于139.8kHz时钟
    float clk2_multiplier = 40.48f;  // 对于40.48kHz时钟
    
    float clk1_freq = filter_freq * clk1_multiplier;
    float clk2_freq = filter_freq * clk2_multiplier;
    
    printf("=== 自动设置时钟频率 ===\r\n");
    printf("滤波器频率: %.2f Hz\r\n", filter_freq);
    printf("计算时钟1频率: %.1f Hz\r\n", clk1_freq);
    printf("计算时钟2频率: %.1f Hz\r\n", clk2_freq);
    
    // 设置时钟频率
    if (MAX262_Clock1_SetFrequency(clk1_freq) != 0)
    {
        printf("警告: 时钟1频率设置失败，使用默认值\r\n");
        MAX262_Clock1_SetFrequency(MAX262_DEFAULT_CLK1_FREQ);
    }
    
    if (MAX262_Clock2_SetFrequency(clk2_freq) != 0)
    {
        printf("警告: 时钟2频率设置失败，使用默认值\r\n");
        MAX262_Clock2_SetFrequency(MAX262_DEFAULT_CLK2_FREQ);
    }
    
    printf("时钟频率自动设置完成\r\n\r\n");
    
    return 0;
}

/**
 * @brief  打印时钟状态信息
 * @param  None
 * @retval None
 */
void MAX262_Clock_PrintStatus(void)
{
    printf("=== MAX262时钟状态 ===\r\n");
    printf("时钟1 (PA8): %.1f Hz, %s\r\n", 
           current_clk1_freq, clk1_enabled ? "运行中" : "已停止");
    printf("时钟2 (PA0): %.1f Hz, %s\r\n", 
           current_clk2_freq, clk2_enabled ? "运行中" : "已停止");
    printf("=====================\r\n\r\n");
}

//-----------------------------------------------------------------
// 私有函数实现
//-----------------------------------------------------------------

/**
 * @brief  计算PWM参数
 * @param  target_freq: 目标频率 (Hz)
 * @param  tim_clk: 定时器时钟频率 (Hz)
 * @param  arr: 输出ARR值
 * @param  psc: 输出PSC值
 * @retval 0: 成功, 1: 无法计算合适参数
 */
static uint8_t MAX262_Calculate_PWM_Params(float target_freq, uint32_t tim_clk, uint32_t* arr, uint32_t* psc)
{
    uint32_t best_arr = 0, best_psc = 0;
    float best_error = 1000000.0f;
    float actual_freq, error;

    // 遍历可能的预分频值 (1-65536)
    for (uint32_t psc_val = 1; psc_val <= 65536; psc_val++)
    {
        // 计算ARR值
        uint32_t arr_val = (uint32_t)((float)tim_clk / (target_freq * psc_val)) - 1;

        // ARR值必须在有效范围内
        if (arr_val < 1 || arr_val > 65535)
            continue;

        // 计算实际频率
        actual_freq = (float)tim_clk / ((arr_val + 1) * psc_val);

        // 计算误差
        error = (actual_freq > target_freq) ? (actual_freq - target_freq) : (target_freq - actual_freq);
        error = error / target_freq * 100.0f;  // 转换为百分比

        // 如果误差更小，更新最佳参数
        if (error < best_error)
        {
            best_error = error;
            best_arr = arr_val;
            best_psc = psc_val - 1;  // PSC寄存器值 = 预分频值 - 1

            // 如果误差足够小，直接使用
            if (error < 0.1f)
                break;
        }
    }

    // 检查是否找到合适的参数
    if (best_error > 5.0f)  // 误差超过5%
    {
        printf("警告: PWM参数计算误差较大: %.2f%%\r\n", best_error);
        return 1;
    }

    *arr = best_arr;
    *psc = best_psc;

    return 0;
}

/**
 * @brief  初始化TIM1 PWM (时钟1)
 * @param  arr: 自动重装载值
 * @param  psc: 预分频值
 * @retval None
 */
static void MAX262_TIM1_PWM_Init(uint32_t arr, uint32_t psc)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_OCInitTypeDef TIM_OCInitStructure;

    // 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);
    MAX262_CLK1_GPIO_CLK_ENABLE();

    // 配置GPIO
    GPIO_PinAFConfig(MAX262_CLK1_GPIO_PORT, MAX262_CLK1_AF_PIN, MAX262_CLK1_AF);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(MAX262_CLK1_GPIO_PORT, &GPIO_InitStructure);

    // 配置定时器基本参数
    TIM_TimeBaseStructure.TIM_Prescaler = psc;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseStructure.TIM_Period = arr;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;
    TIM_TimeBaseInit(TIM1, &TIM_TimeBaseStructure);

    // 配置PWM模式
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_OutputNState = TIM_OutputNState_Disable;
    TIM_OCInitStructure.TIM_Pulse = arr / 2;  // 50%占空比
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
    TIM_OCInitStructure.TIM_OCNPolarity = TIM_OCNPolarity_High;
    TIM_OCInitStructure.TIM_OCIdleState = TIM_OCIdleState_Reset;
    TIM_OCInitStructure.TIM_OCNIdleState = TIM_OCNIdleState_Reset;
    TIM_OC1Init(TIM1, &TIM_OCInitStructure);

    TIM_OC1PreloadConfig(TIM1, TIM_OCPreload_Enable);
    TIM_ARRPreloadConfig(TIM1, ENABLE);

    // 使能主输出 (TIM1需要)
    TIM_CtrlPWMOutputs(TIM1, ENABLE);
}

/**
 * @brief  初始化TIM2 PWM (时钟2)
 * @param  arr: 自动重装载值
 * @param  psc: 预分频值
 * @retval None
 */
static void MAX262_TIM2_PWM_Init(uint32_t arr, uint32_t psc)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_OCInitTypeDef TIM_OCInitStructure;

    // 使能时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
    MAX262_CLK2_GPIO_CLK_ENABLE();

    // 配置GPIO
    GPIO_PinAFConfig(MAX262_CLK2_GPIO_PORT, MAX262_CLK2_AF_PIN, MAX262_CLK2_AF);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(MAX262_CLK2_GPIO_PORT, &GPIO_InitStructure);

    // 配置定时器基本参数
    TIM_TimeBaseStructure.TIM_Prescaler = psc;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseStructure.TIM_Period = arr;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);

    // 配置PWM模式
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_Pulse = arr / 2;  // 50%占空比
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
    TIM_OC1Init(TIM2, &TIM_OCInitStructure);

    TIM_OC1PreloadConfig(TIM2, TIM_OCPreload_Enable);
    TIM_ARRPreloadConfig(TIM2, ENABLE);
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------

# MAX262滤波器驱动 - STM32F407ZG适配版

## 概述

本驱动程序将原始的STM32F103 MAX262驱动适配到STM32F407ZG，并增加了与主程序滤波器学习功能的集成。MAX262是一个双通道、通用、连续时间有源滤波器，支持低通、高通、带通和带阻四种滤波模式。

## 文件说明

- `MAX262_STM32F407.h` - 主驱动头文件，包含引脚定义和基本函数声明
- `MAX262_STM32F407.c` - 主驱动实现文件，包含硬件控制和配置函数
- `MAX262_clock.h` - 时钟生成模块头文件
- `MAX262_clock.c` - 时钟生成模块实现，使用STM32定时器PWM生成时钟
- `MAX262_integration.h` - 集成功能头文件
- `MAX262_integration.c` - 集成功能实现，与主程序滤波器学习功能对接
- `MAX262_example.c` - 使用示例和说明
- `README.md` - 本说明文件

## 引脚配置

### STM32F407ZG引脚分配

| MAX262引脚 | STM32F407引脚 | 功能说明 |
|-----------|--------------|----------|
| D0        | PB0          | 数据位0  |
| D1        | PB1          | 数据位1  |
| A0        | PB2          | 地址位0  |
| A1        | PB3          | 地址位1  |
| A2        | PB4          | 地址位2  |
| A3        | PB5          | 地址位3  |
| LE        | PC0          | 锁存使能 |
| WR        | PC2          | 写使能   |
| CLK1      | PA8          | 时钟1输出 |
| CLK2      | PA0          | 时钟2输出 |

### 硬件连接

```
MAX262模块          STM32F407ZG开发板
---------          ----------------
VCC        <--->   3.3V
GND        <--->   GND
D0         <--->   PB0
D1         <--->   PB1
A0         <--->   PB2
A1         <--->   PB3
A2         <--->   PB4
A3         <--->   PB5
LE         <--->   PC0
WR         <--->   PC2
CLK1       <--->   PA8 (STM32生成的时钟1)
CLK2       <--->   PA0 (STM32生成的时钟2)
IN1        <--->   信号输入1
OUT1       <--->   滤波器1输出
IN2        <--->   信号输入2
OUT2       <--->   滤波器2输出
```

## 使用方法

### 1. 基本初始化

```c
#include "MAX262_integration.h"

int main(void)
{
    // ... 其他初始化代码 ...
    
    // 初始化MAX262
    if (MAX262_System_Init() == 0)
    {
        printf("MAX262初始化成功\r\n");
    }
    else
    {
        printf("MAX262初始化失败\r\n");
    }
    
    // ... 主循环 ...
}
```

### 2. 手动配置滤波器

```c
// 配置1kHz低通滤波器，Q=0.707
MAX262_Configure_From_Learning(1000.0f, 0.707f, 0);

// 配置5kHz带通滤波器，Q=2.0
MAX262_Configure_From_Learning(5000.0f, 2.0f, 2);

// 配置10kHz高通滤波器，Q=1.0
MAX262_Configure_From_Learning(10000.0f, 1.0f, 1);
```

### 3. 自动配置（与学习功能集成）

```c
// 在滤波器识别完成后自动配置MAX262
if (MAX262_Auto_Configure_From_Main() == 0)
{
    printf("MAX262自动配置成功\r\n");
}
```

### 4. 在主程序中集成

在你的main.c文件中添加以下代码：

```c
// 在文件顶部添加头文件
#include "MAX262_integration.h"

// 在main函数的初始化部分添加
int main(void)
{
    // ... 现有的初始化代码 ...
    
    // 初始化MAX262
    MAX262_System_Init();
    
    // ... 现有的主循环代码 ...
    
    while(1)
    {
        // ... 现有的代码 ...
        
        // 在滤波器识别完成后，添加以下代码
        // (例如在IdentifyCircuitModel函数调用后)
        if (/* 滤波器识别完成的条件 */)
        {
            // 自动配置MAX262以匹配识别的滤波器
            MAX262_Auto_Configure_From_Main();
        }
        
        // ... 其他代码 ...
    }
}
```

## 滤波器模式

MAX262支持四种滤波器模式：

| 模式值 | 滤波器类型 | 说明 |
|-------|-----------|------|
| 0     | 低通滤波器 | 允许低于截止频率的信号通过 |
| 1     | 高通滤波器 | 允许高于截止频率的信号通过 |
| 2     | 带通滤波器 | 允许特定频带内的信号通过 |
| 3     | 带阻滤波器 | 阻止特定频带内的信号通过 |

## 参数范围

- **频率范围**: 1 Hz - 50 kHz (取决于时钟频率)
- **Q值范围**: 0.1 - 64
- **时钟频率**:
  - 滤波器1: 1 kHz - 1 MHz (STM32 PWM生成)
  - 滤波器2: 1 kHz - 1 MHz (STM32 PWM生成)
  - 默认频率: 139.8 kHz (滤波器1), 40.48 kHz (滤波器2)

## 时钟生成功能

### 自动时钟调整
驱动程序会根据滤波器工作频率自动调整时钟频率，确保最佳性能：

```c
// 自动根据滤波器频率设置时钟
MAX262_Clock_AutoSet(filter_frequency);
```

### 手动时钟控制
你也可以手动控制时钟频率：

```c
// 设置时钟1为100kHz
MAX262_Clock1_SetFrequency(100000.0f);

// 设置时钟2为50kHz
MAX262_Clock2_SetFrequency(50000.0f);

// 启动/停止时钟输出
MAX262_Clock1_Start();
MAX262_Clock2_Start();
MAX262_Clock1_Stop();
MAX262_Clock2_Stop();

// 查看时钟状态
MAX262_Clock_PrintStatus();
```

### 时钟输出引脚
- **时钟1**: PA8 (TIM1_CH1) - 50%占空比PWM
- **时钟2**: PA0 (TIM2_CH1) - 50%占空比PWM

## 注意事项

1. **时钟配置**: 确保为MAX262提供正确的时钟信号，时钟频率直接影响滤波器的工作频率范围。

2. **引脚冲突**: 所选引脚均为项目中未使用的GPIO，不会与现有功能冲突。

3. **电源要求**: MAX262工作电压为3.3V，与STM32F407ZG兼容。

4. **信号范围**: 输入信号幅度应在MAX262的工作范围内。

5. **频率计算**: 驱动程序会自动计算频率和Q值对应的控制字，无需手动计算。

## 调试功能

```c
// 打印MAX262状态信息
MAX262_Print_Status();

// 测试不同配置
MAX262_Test_Configuration();
```

## 编译配置

确保在项目中包含以下文件：
- `MAX262_STM32F407.c`
- `MAX262_integration.c`

并在包含路径中添加：
- `hardware/max262`

## 故障排除

1. **初始化失败**: 检查引脚连接和时钟配置
2. **配置无效**: 检查频率和Q值是否在有效范围内
3. **输出异常**: 检查时钟信号和输入信号幅度

## 版本历史

- V1.0 (2024-08-03): 初始版本，适配STM32F407ZG
  - 从STM32F103移植
  - 增加频率自动计算功能
  - 集成主程序滤波器学习功能
  - 添加完整的错误处理和调试功能

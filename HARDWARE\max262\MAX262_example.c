//-----------------------------------------------------------------
// MAX262使用示例 - 与学习滤波器参数匹配
// 文件名: MAX262_example.c
// 说明: 展示如何根据学习到的滤波器参数配置MAX262
//-----------------------------------------------------------------

#include "MAX262_STM32F407.h"
#include "stdio.h"

//-----------------------------------------------------------------
// 示例：根据学习到的滤波器参数配置MAX262
//-----------------------------------------------------------------
void MAX262_Configure_From_Learning(float center_freq, float q_factor, uint8_t filter_type)
{
    uint8_t mode1, mode3;
    
    // 根据滤波器类型选择模式
    switch(filter_type)
    {
        case 0: // 低通滤波器
            mode1 = MAX262_MODE_1_LP;
            mode3 = MAX262_MODE_3_LP;
            printf("配置MAX262为低通滤波器\r\n");
            break;
            
        case 1: // 高通滤波器
            mode1 = MAX262_MODE_1_HP;
            mode3 = MAX262_MODE_3_HP;
            printf("配置MAX262为高通滤波器\r\n");
            break;
            
        case 2: // 带通滤波器
            mode1 = MAX262_MODE_1_BP;
            mode3 = MAX262_MODE_3_BP;
            printf("配置MAX262为带通滤波器\r\n");
            break;
            
        case 3: // 带阻滤波器
            mode1 = MAX262_MODE_1_BS;
            mode3 = MAX262_MODE_3_BS;
            printf("配置MAX262为带阻滤波器\r\n");
            break;
            
        default:
            mode1 = MAX262_MODE_1_LP;
            mode3 = MAX262_MODE_3_LP;
            printf("默认配置MAX262为低通滤波器\r\n");
            break;
    }
    
    // 打印配置信息
    printf("中心频率/截止频率: %.2f Hz\r\n", center_freq);
    printf("品质因数Q: %.2f\r\n", q_factor);
    
    // 配置滤波器1 (使用模式1)
    MAX262_Filter1_Config(mode1, center_freq, q_factor);
    printf("滤波器1配置完成\r\n");
    
    // 配置滤波器2 (使用模式3)
    MAX262_Filter2_Config(mode3, center_freq, q_factor);
    printf("滤波器2配置完成\r\n");
    
    printf("MAX262配置完成，可以开始滤波\r\n\r\n");
}

//-----------------------------------------------------------------
// 示例：在主程序中的使用方法
//-----------------------------------------------------------------
void MAX262_Usage_Example(void)
{
    // 初始化MAX262
    printf("=== MAX262初始化 ===\r\n");
    MAX262_Init();
    printf("MAX262初始化完成\r\n\r\n");
    
    // 示例1：配置为1kHz低通滤波器，Q=0.707
    printf("=== 示例1：1kHz低通滤波器 ===\r\n");
    MAX262_Configure_From_Learning(1000.0f, 0.707f, 0);
    
    delay_ms(1000);
    
    // 示例2：配置为5kHz带通滤波器，Q=2.0
    printf("=== 示例2：5kHz带通滤波器 ===\r\n");
    MAX262_Configure_From_Learning(5000.0f, 2.0f, 2);
    
    delay_ms(1000);
    
    // 示例3：配置为10kHz高通滤波器，Q=1.0
    printf("=== 示例3：10kHz高通滤波器 ===\r\n");
    MAX262_Configure_From_Learning(10000.0f, 1.0f, 1);
}

//-----------------------------------------------------------------
// 在你的主程序中添加以下代码来使用MAX262
//-----------------------------------------------------------------
/*
// 在main.c中添加头文件
#include "MAX262_STM32F407.h"

// 在main函数中初始化MAX262
int main(void)
{
    // ... 你的其他初始化代码 ...
    
    // 初始化MAX262
    MAX262_Init();
    printf("MAX262初始化完成\r\n");
    
    // ... 你的主循环 ...
    
    while(1)
    {
        // 当你的程序计算出滤波器参数后，调用配置函数
        // 例如：
        // float learned_freq = 2000.0f;  // 学习到的中心频率
        // float learned_q = 1.5f;        // 学习到的Q值
        // uint8_t filter_type = 2;       // 带通滤波器
        // MAX262_Configure_From_Learning(learned_freq, learned_q, filter_type);
        
        // ... 你的其他代码 ...
    }
}
*/

//-----------------------------------------------------------------
// 引脚连接说明
//-----------------------------------------------------------------
/*
MAX262引脚连接到STM32F407ZG：

MAX262引脚    STM32F407引脚    功能
---------    -------------    ----
D0           PB0              数据位0
D1           PB1              数据位1
A0           PB2              地址位0
A1           PB3              地址位1
A2           PB4              地址位2
A3           PB5              地址位3
LE           PC0              锁存使能
WR           PC2              写使能
VCC          3.3V             电源正极
GND          GND              电源负极
CLK1         外部时钟1         滤波器1时钟输入 (139.8kHz)
CLK2         外部时钟2         滤波器2时钟输入 (40.48kHz)
IN1          信号输入1         滤波器1信号输入
OUT1         信号输出1         滤波器1信号输出
IN2          信号输入2         滤波器2信号输入
OUT2         信号输出2         滤波器2信号输出

注意：
1. 时钟频率需要根据你的硬件配置调整MAX262_CLK_FREQ_1和MAX262_CLK_FREQ_2
2. 所选引脚均为未使用的GPIO引脚，不会与现有功能冲突
3. 可以根据需要只使用滤波器1或滤波器2
*/

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// MAX262时钟生成模块 - STM32F407ZG
// 文件名: MAX262_clock.h
// 说明: 使用STM32F407ZG的定时器PWM功能为MAX262生成时钟信号
//-----------------------------------------------------------------

#ifndef _MAX262_CLOCK_H
#define _MAX262_CLOCK_H

#include "stm32f4xx.h"
#include "sys.h"

//-----------------------------------------------------------------
// 时钟输出引脚定义
//-----------------------------------------------------------------
// 时钟1输出引脚 - 使用TIM1_CH1 (PA8)
#define MAX262_CLK1_GPIO_PORT           GPIOA
#define MAX262_CLK1_GPIO_PIN            SYS_GPIO_PIN8
#define MAX262_CLK1_GPIO_CLK_ENABLE()   do{ RCC->AHB1ENR |= 1 << 0; }while(0)
#define MAX262_CLK1_AF_PIN              GPIO_PinSource8
#define MAX262_CLK1_AF                  GPIO_AF_TIM1

// 时钟2输出引脚 - 使用TIM2_CH1 (PA0)
#define MAX262_CLK2_GPIO_PORT           GPIOA
#define MAX262_CLK2_GPIO_PIN            SYS_GPIO_PIN0
#define MAX262_CLK2_GPIO_CLK_ENABLE()   do{ RCC->AHB1ENR |= 1 << 0; }while(0)
#define MAX262_CLK2_AF_PIN              GPIO_PinSource0
#define MAX262_CLK2_AF                  GPIO_AF_TIM2

//-----------------------------------------------------------------
// 默认时钟频率定义
//-----------------------------------------------------------------
#define MAX262_DEFAULT_CLK1_FREQ        139800.0f   // 139.8kHz
#define MAX262_DEFAULT_CLK2_FREQ        40480.0f    // 40.48kHz

// 时钟频率范围限制
#define MAX262_MIN_CLK_FREQ             1000.0f     // 最小1kHz
#define MAX262_MAX_CLK_FREQ             1000000.0f  // 最大1MHz

// STM32F407ZG定时器时钟频率 (APB1: 42MHz, APB2: 84MHz)
#define TIM1_CLK_FREQ                   84000000    // TIM1在APB2上，84MHz
#define TIM2_CLK_FREQ                   84000000    // TIM2在APB1上，但由于APB1预分频器≠1，所以×2 = 84MHz

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------

/**
 * @brief  初始化MAX262时钟生成器
 * @param  None
 * @retval 0: 成功, 1: 失败
 */
uint8_t MAX262_Clock_Init(void);

/**
 * @brief  设置时钟1频率
 * @param  frequency: 目标频率 (Hz)
 * @retval 0: 成功, 1: 频率超出范围
 */
uint8_t MAX262_Clock1_SetFrequency(float frequency);

/**
 * @brief  设置时钟2频率
 * @param  frequency: 目标频率 (Hz)
 * @retval 0: 成功, 1: 频率超出范围
 */
uint8_t MAX262_Clock2_SetFrequency(float frequency);

/**
 * @brief  启动时钟1输出
 * @param  None
 * @retval None
 */
void MAX262_Clock1_Start(void);

/**
 * @brief  停止时钟1输出
 * @param  None
 * @retval None
 */
void MAX262_Clock1_Stop(void);

/**
 * @brief  启动时钟2输出
 * @param  None
 * @retval None
 */
void MAX262_Clock2_Start(void);

/**
 * @brief  停止时钟2输出
 * @param  None
 * @retval None
 */
void MAX262_Clock2_Stop(void);

/**
 * @brief  获取当前时钟1频率
 * @param  None
 * @retval 当前时钟1频率 (Hz)
 */
float MAX262_Clock1_GetFrequency(void);

/**
 * @brief  获取当前时钟2频率
 * @param  None
 * @retval 当前时钟2频率 (Hz)
 */
float MAX262_Clock2_GetFrequency(void);

/**
 * @brief  根据滤波器频率自动设置时钟频率
 * @param  filter_freq: 滤波器工作频率 (Hz)
 * @retval 0: 成功, 1: 频率超出范围
 * @note   时钟频率 = 滤波器频率 × 倍数因子
 */
uint8_t MAX262_Clock_AutoSet(float filter_freq);

/**
 * @brief  打印时钟状态信息
 * @param  None
 * @retval None
 */
void MAX262_Clock_PrintStatus(void);

#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------

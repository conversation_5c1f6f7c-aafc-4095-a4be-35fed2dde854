//-----------------------------------------------------------------
// MAX262时钟生成使用示例
// 文件名: clock_example.c
// 说明: 展示如何使用STM32生成MAX262时钟信号
//-----------------------------------------------------------------

#include "MAX262_clock.h"
#include "MAX262_integration.h"
#include "delay.h"
#include "usart.h"

//-----------------------------------------------------------------
// 时钟测试函数
//-----------------------------------------------------------------

/**
 * @brief  测试基本时钟功能
 * @param  None
 * @retval None
 */
void Test_Basic_Clock_Functions(void)
{
    printf("=== 基本时钟功能测试 ===\r\n");
    
    // 初始化时钟系统
    if (MAX262_Clock_Init() == 0)
    {
        printf("时钟系统初始化成功\r\n");
    }
    else
    {
        printf("时钟系统初始化失败\r\n");
        return;
    }
    
    // 显示初始状态
    MAX262_Clock_PrintStatus();
    
    // 测试不同频率设置
    float test_frequencies[] = {10000.0f, 50000.0f, 100000.0f, 200000.0f};
    int num_tests = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    
    for (int i = 0; i < num_tests; i++)
    {
        printf("\n--- 测试频率: %.1f Hz ---\r\n", test_frequencies[i]);
        
        // 设置时钟1
        if (MAX262_Clock1_SetFrequency(test_frequencies[i]) == 0)
        {
            printf("时钟1设置成功\r\n");
        }
        else
        {
            printf("时钟1设置失败\r\n");
        }
        
        // 设置时钟2为时钟1的一半
        if (MAX262_Clock2_SetFrequency(test_frequencies[i] / 2.0f) == 0)
        {
            printf("时钟2设置成功\r\n");
        }
        else
        {
            printf("时钟2设置失败\r\n");
        }
        
        // 显示当前状态
        MAX262_Clock_PrintStatus();
        
        delay_ms(2000);  // 等待2秒
    }
    
    printf("基本时钟功能测试完成\r\n\r\n");
}

/**
 * @brief  测试自动时钟调整功能
 * @param  None
 * @retval None
 */
void Test_Auto_Clock_Adjustment(void)
{
    printf("=== 自动时钟调整测试 ===\r\n");
    
    // 测试不同滤波器频率的自动时钟调整
    float filter_frequencies[] = {100.0f, 500.0f, 1000.0f, 5000.0f, 10000.0f};
    int num_tests = sizeof(filter_frequencies) / sizeof(filter_frequencies[0]);
    
    for (int i = 0; i < num_tests; i++)
    {
        printf("\n--- 滤波器频率: %.1f Hz ---\r\n", filter_frequencies[i]);
        
        // 自动设置时钟频率
        if (MAX262_Clock_AutoSet(filter_frequencies[i]) == 0)
        {
            printf("自动时钟调整成功\r\n");
        }
        else
        {
            printf("自动时钟调整失败\r\n");
        }
        
        // 显示调整后的时钟状态
        MAX262_Clock_PrintStatus();
        
        delay_ms(1500);  // 等待1.5秒
    }
    
    printf("自动时钟调整测试完成\r\n\r\n");
}

/**
 * @brief  测试时钟启停控制
 * @param  None
 * @retval None
 */
void Test_Clock_Start_Stop(void)
{
    printf("=== 时钟启停控制测试 ===\r\n");
    
    // 设置测试频率
    MAX262_Clock1_SetFrequency(100000.0f);  // 100kHz
    MAX262_Clock2_SetFrequency(50000.0f);   // 50kHz
    
    printf("初始状态:\r\n");
    MAX262_Clock_PrintStatus();
    
    // 测试启动时钟1
    printf("启动时钟1...\r\n");
    MAX262_Clock1_Start();
    delay_ms(1000);
    
    // 测试启动时钟2
    printf("启动时钟2...\r\n");
    MAX262_Clock2_Start();
    delay_ms(1000);
    
    printf("两个时钟都运行中:\r\n");
    MAX262_Clock_PrintStatus();
    
    // 测试停止时钟1
    printf("停止时钟1...\r\n");
    MAX262_Clock1_Stop();
    delay_ms(1000);
    
    // 测试停止时钟2
    printf("停止时钟2...\r\n");
    MAX262_Clock2_Stop();
    delay_ms(1000);
    
    printf("最终状态:\r\n");
    MAX262_Clock_PrintStatus();
    
    printf("时钟启停控制测试完成\r\n\r\n");
}

/**
 * @brief  完整的MAX262时钟测试
 * @param  None
 * @retval None
 */
void Test_Complete_MAX262_Clock_System(void)
{
    printf("=== 完整MAX262时钟系统测试 ===\r\n");
    
    // 初始化完整的MAX262系统
    if (MAX262_System_Init() == 0)
    {
        printf("MAX262系统初始化成功\r\n");
    }
    else
    {
        printf("MAX262系统初始化失败\r\n");
        return;
    }
    
    // 测试滤波器配置与时钟自动调整的集成
    printf("\n测试滤波器配置与时钟集成...\r\n");
    
    // 配置1kHz带通滤波器
    printf("配置1kHz带通滤波器...\r\n");
    MAX262_Configure_From_Learning(1000.0f, 2.0f, 2);  // 1kHz, Q=2.0, 带通
    delay_ms(2000);
    
    // 配置5kHz低通滤波器
    printf("配置5kHz低通滤波器...\r\n");
    MAX262_Configure_From_Learning(5000.0f, 0.707f, 0);  // 5kHz, Q=0.707, 低通
    delay_ms(2000);
    
    // 配置10kHz高通滤波器
    printf("配置10kHz高通滤波器...\r\n");
    MAX262_Configure_From_Learning(10000.0f, 1.0f, 1);  // 10kHz, Q=1.0, 高通
    delay_ms(2000);
    
    // 显示最终状态
    printf("最终系统状态:\r\n");
    MAX262_Print_Status();
    MAX262_Clock_PrintStatus();
    
    printf("完整MAX262时钟系统测试完成\r\n\r\n");
}

/**
 * @brief  主测试函数 - 在main.c中调用
 * @param  None
 * @retval None
 */
void MAX262_Clock_Test_All(void)
{
    printf("\n");
    printf("*************************************************\r\n");
    printf("*          MAX262时钟系统完整测试               *\r\n");
    printf("*************************************************\r\n");
    
    // 运行所有测试
    Test_Basic_Clock_Functions();
    delay_ms(1000);
    
    Test_Auto_Clock_Adjustment();
    delay_ms(1000);
    
    Test_Clock_Start_Stop();
    delay_ms(1000);
    
    Test_Complete_MAX262_Clock_System();
    
    printf("*************************************************\r\n");
    printf("*            所有测试完成                       *\r\n");
    printf("*************************************************\r\n");
    printf("\n");
}

/**
 * @brief  简单的时钟测试 - 用于快速验证
 * @param  None
 * @retval None
 */
void MAX262_Clock_Quick_Test(void)
{
    printf("=== MAX262时钟快速测试 ===\r\n");
    
    // 初始化并启动默认时钟
    MAX262_Clock_Init();
    MAX262_Clock1_Start();
    MAX262_Clock2_Start();
    
    // 显示状态
    MAX262_Clock_PrintStatus();
    
    printf("时钟已启动，可以使用示波器在PA8和PA0引脚观察时钟信号\r\n");
    printf("时钟快速测试完成\r\n\r\n");
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------

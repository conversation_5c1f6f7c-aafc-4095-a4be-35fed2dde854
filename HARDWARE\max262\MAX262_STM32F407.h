//-----------------------------------------------------------------
// MAX262驱动头文件 - 适配STM32F407ZG
// 头文件名: MAX262_STM32F407.h
// 作    者: 正点原子 (修改适配STM32F407ZG)
// 编写时间: 2014-01-28
// 修改记录: 2024-08-03 - 适配STM32F407ZG，使用未使用的引脚
//-----------------------------------------------------------------

#ifndef _MAX262_STM32F407_H
#define _MAX262_STM32F407_H

#include "stm32f4xx.h"
#include "sys.h"
#include "delay.h"
#include "MAX262_clock.h"
#include <math.h>

//-----------------------------------------------------------------
// 引脚定义 - 使用未使用的GPIO引脚
//-----------------------------------------------------------------
// 数据引脚 D0, D1 - 使用GPIOB未使用的引脚
#define MAX262_D0_GPIO_PORT         GPIOB
#define MAX262_D0_GPIO_PIN          SYS_GPIO_PIN0
#define MAX262_D0_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 1; }while(0)

#define MAX262_D1_GPIO_PORT         GPIOB
#define MAX262_D1_GPIO_PIN          SYS_GPIO_PIN1
#define MAX262_D1_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 1; }while(0)

// 地址引脚 A0-A3 - 使用GPIOB未使用的引脚
#define MAX262_A0_GPIO_PORT         GPIOB
#define MAX262_A0_GPIO_PIN          SYS_GPIO_PIN2
#define MAX262_A0_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 1; }while(0)

#define MAX262_A1_GPIO_PORT         GPIOB
#define MAX262_A1_GPIO_PIN          SYS_GPIO_PIN3
#define MAX262_A1_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 1; }while(0)

#define MAX262_A2_GPIO_PORT         GPIOB
#define MAX262_A2_GPIO_PIN          SYS_GPIO_PIN4
#define MAX262_A2_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 1; }while(0)

#define MAX262_A3_GPIO_PORT         GPIOB
#define MAX262_A3_GPIO_PIN          SYS_GPIO_PIN5
#define MAX262_A3_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 1; }while(0)

// 控制引脚 LE, WR - 使用GPIOC未使用的引脚
#define MAX262_LE_GPIO_PORT         GPIOC
#define MAX262_LE_GPIO_PIN          SYS_GPIO_PIN0
#define MAX262_LE_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)

#define MAX262_WR_GPIO_PORT         GPIOC
#define MAX262_WR_GPIO_PIN          SYS_GPIO_PIN2
#define MAX262_WR_GPIO_CLK_ENABLE() do{ RCC->AHB1ENR |= 1 << 2; }while(0)

//-----------------------------------------------------------------
// GPIO操作宏定义
//-----------------------------------------------------------------
#define D0_L    sys_gpio_pin_set(MAX262_D0_GPIO_PORT, MAX262_D0_GPIO_PIN, 0)
#define D0_H    sys_gpio_pin_set(MAX262_D0_GPIO_PORT, MAX262_D0_GPIO_PIN, 1)

#define D1_L    sys_gpio_pin_set(MAX262_D1_GPIO_PORT, MAX262_D1_GPIO_PIN, 0)
#define D1_H    sys_gpio_pin_set(MAX262_D1_GPIO_PORT, MAX262_D1_GPIO_PIN, 1)

#define A0_L    sys_gpio_pin_set(MAX262_A0_GPIO_PORT, MAX262_A0_GPIO_PIN, 0)
#define A0_H    sys_gpio_pin_set(MAX262_A0_GPIO_PORT, MAX262_A0_GPIO_PIN, 1)
#define A0_IS_L (sys_gpio_pin_get(MAX262_A0_GPIO_PORT, MAX262_A0_GPIO_PIN) == 0)

#define A1_L    sys_gpio_pin_set(MAX262_A1_GPIO_PORT, MAX262_A1_GPIO_PIN, 0)
#define A1_H    sys_gpio_pin_set(MAX262_A1_GPIO_PORT, MAX262_A1_GPIO_PIN, 1)
#define A1_IS_H (sys_gpio_pin_get(MAX262_A1_GPIO_PORT, MAX262_A1_GPIO_PIN) == 1)

#define A2_L    sys_gpio_pin_set(MAX262_A2_GPIO_PORT, MAX262_A2_GPIO_PIN, 0)
#define A2_H    sys_gpio_pin_set(MAX262_A2_GPIO_PORT, MAX262_A2_GPIO_PIN, 1)

#define A3_L    sys_gpio_pin_set(MAX262_A3_GPIO_PORT, MAX262_A3_GPIO_PIN, 0)
#define A3_H    sys_gpio_pin_set(MAX262_A3_GPIO_PORT, MAX262_A3_GPIO_PIN, 1)

#define LE_L    sys_gpio_pin_set(MAX262_LE_GPIO_PORT, MAX262_LE_GPIO_PIN, 0)
#define LE_H    sys_gpio_pin_set(MAX262_LE_GPIO_PORT, MAX262_LE_GPIO_PIN, 1)

#define WR_L    sys_gpio_pin_set(MAX262_WR_GPIO_PORT, MAX262_WR_GPIO_PIN, 0)
#define WR_H    sys_gpio_pin_set(MAX262_WR_GPIO_PORT, MAX262_WR_GPIO_PIN, 1)

//-----------------------------------------------------------------
// MAX262滤波器模式定义
//-----------------------------------------------------------------
#define MAX262_MODE_1_LP    0x00    // 模式1: 低通滤波器
#define MAX262_MODE_1_HP    0x01    // 模式1: 高通滤波器
#define MAX262_MODE_1_BP    0x02    // 模式1: 带通滤波器
#define MAX262_MODE_1_BS    0x03    // 模式1: 带阻滤波器

#define MAX262_MODE_3_LP    0x00    // 模式3: 低通滤波器
#define MAX262_MODE_3_HP    0x01    // 模式3: 高通滤波器
#define MAX262_MODE_3_BP    0x02    // 模式3: 带通滤波器
#define MAX262_MODE_3_BS    0x03    // 模式3: 带阻滤波器

//-----------------------------------------------------------------
// 时钟频率定义 - 现在由时钟生成模块动态提供
//-----------------------------------------------------------------
// 这些宏现在用于获取动态时钟频率
#define MAX262_CLK_FREQ_1   MAX262_Clock1_GetFrequency()   // 滤波器1时钟频率
#define MAX262_CLK_FREQ_2   MAX262_Clock2_GetFrequency()   // 滤波器2时钟频率

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------
void MAX262_GPIO_Init(void);
void MAX262_Init(void);
uint8_t MAX262_Fn_Calculate(float frequency, float clk_freq);
uint8_t MAX262_Qn_Calculate(float q);
void MAX262_Filter1_Config(uint8_t mode, float frequency, float q);
void MAX262_Filter2_Config(uint8_t mode, float frequency, float q);
void MAX262_Write_Register(uint8_t address, uint8_t data);

#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------

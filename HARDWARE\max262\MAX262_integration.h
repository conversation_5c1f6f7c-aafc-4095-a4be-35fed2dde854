//-----------------------------------------------------------------
// MAX262集成头文件
// 文件名: MAX262_integration.h
// 说明: MAX262与主程序集成的函数声明
//-----------------------------------------------------------------

#ifndef _MAX262_INTEGRATION_H
#define _MAX262_INTEGRATION_H

#include "MAX262_STM32F407.h"

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------

/**
 * @brief  初始化MAX262硬件
 * @param  None
 * @retval 0: 成功, 1: 失败
 */
uint8_t MAX262_System_Init(void);

/**
 * @brief  根据学习到的滤波器参数配置MAX262
 * @param  learned_freq: 学习到的中心频率/截止频率 (Hz)
 * @param  learned_q: 学习到的品质因数
 * @param  filter_type: 滤波器类型 (0:低通, 1:高通, 2:带通, 3:带阻)
 * @retval 0: 成功, 1: 参数错误, 2: 未初始化
 */
uint8_t MAX262_Configure_From_Learning(float learned_freq, float learned_q, uint8_t filter_type);

/**
 * @brief  自动从主程序的滤波器识别结果配置MAX262
 * @param  None
 * @retval 0: 成功, 1: 无有效数据, 2: 配置失败
 */
uint8_t MAX262_Auto_Configure_From_Main(void);

/**
 * @brief  测试MAX262配置
 * @param  None
 * @retval None
 */
void MAX262_Test_Configuration(void);

/**
 * @brief  获取MAX262状态信息
 * @param  None
 * @retval None
 */
void MAX262_Print_Status(void);

#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
